#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后的分段搜索演示 - 复用search_along_route方法
展示如何使用段的起点和终点直接调用现有的搜索方法
"""

import math
from typing import List, Tuple, Dict, Any


class OptimizedSegmentDemo:
    """优化后的分段搜索演示类"""
    
    def __init__(self):
        self.poi_types = {
            "加油站": "010300",
            "餐厅": "050000", 
            "服务区": "180300",
            "充电站": "010301"
        }
    
    def generate_mock_route(self, start_lng: float, start_lat: float, 
                           end_lng: float, end_lat: float, 
                           num_points: int = 100) -> List[Tuple[float, float]]:
        """生成模拟路线坐标点"""
        coordinates = []
        
        for i in range(num_points):
            ratio = i / (num_points - 1)
            lng = start_lng + (end_lng - start_lng) * ratio
            lat = start_lat + (end_lat - start_lat) * ratio
            
            # 添加一些随机偏移模拟真实路线的弯曲
            if i > 0 and i < num_points - 1:
                offset = 0.001 * math.sin(i * 0.5)
                lng += offset
                lat += offset * 0.5
                
            coordinates.append((lng, lat))
            
        return coordinates
    
    def split_route_into_segments(self, coordinates: List[Tuple[float, float]], 
                                  max_points_per_segment: int = 30) -> List[List[Tuple[float, float]]]:
        """将长路线分割成多个段"""
        if len(coordinates) <= max_points_per_segment:
            return [coordinates]
        
        segments = []
        overlap_points = 3  # 段间重叠点数，确保连续性
        
        start_idx = 0
        while start_idx < len(coordinates):
            end_idx = min(start_idx + max_points_per_segment, len(coordinates))
            segment = coordinates[start_idx:end_idx]
            segments.append(segment)
            
            if end_idx >= len(coordinates):
                break
                
            start_idx = end_idx - overlap_points
            
        return segments
    
    def mock_search_along_route(self, origin: str, destination: str, 
                               keywords: str, segment_id: int) -> Dict[str, Any]:
        """
        模拟search_along_route方法的调用
        在实际应用中，这里会调用真正的search_along_route方法
        """
        print(f"      🔍 调用 search_along_route(origin={origin}, destination={destination})")
        
        # 模拟API调用延时
        import time
        time.sleep(0.1)
        
        # 模拟搜索结果
        mock_pois = []
        poi_count = 2 + (segment_id % 3)  # 每段2-4个POI
        
        for i in range(poi_count):
            poi = {
                'id': f'seg{segment_id}_poi{i+1}',
                'name': f'{keywords}{segment_id}-{i+1}',
                'address': f'模拟地址{segment_id}-{i+1}',
                'location': f'{float(origin.split(",")[0]) + i*0.001:.6f},{float(origin.split(",")[1]) + i*0.0005:.6f}',
                'segment': segment_id
            }
            mock_pois.append(poi)
        
        return {
            'status': '1',
            'pois': mock_pois,
            'total_count': len(mock_pois),
            'execution_time': 0.1
        }
    
    def demo_optimized_segmented_search(self, start_coords: Tuple[float, float], 
                                       end_coords: Tuple[float, float],
                                       keywords: str = "服务区",
                                       route_points: int = 120,
                                       max_points_per_segment: int = 25) -> Dict[str, Any]:
        """
        演示优化后的分段搜索流程
        """
        print("🗺️  优化后的分段搜索演示")
        print("=" * 50)
        print("💡 核心优化：分段后直接使用起点终点调用 search_along_route()")
        print()
        
        # 1. 生成模拟路线
        print(f"📍 生成模拟路线: {route_points} 个坐标点")
        coordinates = self.generate_mock_route(
            start_coords[0], start_coords[1],
            end_coords[0], end_coords[1], 
            route_points
        )
        
        # 2. 分段处理
        print(f"📏 开始分段处理，每段最多 {max_points_per_segment} 个点")
        segments = self.split_route_into_segments(coordinates, max_points_per_segment)
        
        print(f"📏 路线分段完成: {len(coordinates)} 个点 → {len(segments)} 段")
        for i, segment in enumerate(segments):
            print(f"   段 {i+1}: {len(segment)} 个点")
        
        # 3. 逐段搜索 - 使用起点终点调用search_along_route
        print(f"\n🔍 开始逐段搜索，复用 search_along_route() 方法:")
        all_pois = []
        poi_ids_seen = set()
        
        for i, segment_coords in enumerate(segments):
            print(f"\n🔍 正在搜索第 {i+1}/{len(segments)} 段...")
            
            # 获取当前段的起点和终点坐标
            segment_origin = f"{segment_coords[0][0]},{segment_coords[0][1]}"
            segment_destination = f"{segment_coords[-1][0]},{segment_coords[-1][1]}"
            
            print(f"   段起点: {segment_origin}")
            print(f"   段终点: {segment_destination}")
            
            # 🎯 关键优化：直接使用起点终点调用search_along_route
            segment_result = self.mock_search_along_route(
                origin=segment_origin,
                destination=segment_destination,
                keywords=keywords,
                segment_id=i+1
            )
            
            if segment_result.get('status') == '1':
                segment_pois = segment_result.get('pois', [])
                
                # 去重处理
                new_pois = []
                for poi in segment_pois:
                    poi_id = poi.get('id')
                    if poi_id and poi_id not in poi_ids_seen:
                        poi_ids_seen.add(poi_id)
                        new_pois.append(poi)
                
                all_pois.extend(new_pois)
                print(f"   ✅ 第 {i+1} 段找到 {len(segment_pois)} 个POI，去重后新增 {len(new_pois)} 个")
            else:
                print(f"   ❌ 第 {i+1} 段搜索失败")
        
        # 4. 汇总结果
        result = {
            "status": "success",
            "optimization": "使用段起点终点调用search_along_route",
            "route_info": {
                "total_points": len(coordinates),
                "segments": len(segments),
                "max_points_per_segment": max_points_per_segment
            },
            "search_results": {
                "total_pois": len(all_pois),
                "pois": all_pois
            }
        }
        
        print(f"\n✅ 优化后的分段搜索完成！")
        print(f"   总计找到 {len(all_pois)} 个不重复的{keywords}")
        
        return result


def main():
    """演示主函数"""
    demo = OptimizedSegmentDemo()
    
    # 模拟北京到上海的长距离路线
    start_coords = (116.397128, 39.916527)  # 北京天安门
    end_coords = (121.604218, 31.245483)    # 上海外滩
    
    print("📍 测试路线：北京天安门 → 上海外滩")
    print(f"📍 起点：{start_coords}")
    print(f"📍 终点：{end_coords}")
    print()
    
    # 演示优化后的分段搜索
    result = demo.demo_optimized_segmented_search(
        start_coords=start_coords,
        end_coords=end_coords,
        keywords="服务区",
        route_points=120,  # 模拟120个路线点（长路线）
        max_points_per_segment=25  # 每段最多25个点
    )
    
    # 显示详细结果
    if result.get("status") == "success":
        print("\n" + "=" * 50)
        print("📊 优化效果分析:")
        route_info = result["route_info"]
        search_results = result["search_results"]
        
        print(f"   原始路线点数: {route_info['total_points']}")
        print(f"   分段数量: {route_info['segments']}")
        print(f"   每段最大点数: {route_info['max_points_per_segment']}")
        print(f"   找到POI总数: {search_results['total_pois']}")
        print(f"   优化方案: {result['optimization']}")
        
        # 显示前几个POI
        pois = search_results["pois"]
        if pois:
            print(f"\n📍 POI详情 (前8个):")
            for poi in pois[:8]:
                print(f"   • {poi['name']} (第{poi['segment']}段)")
                print(f"     地址: {poi['address']}")
                print(f"     坐标: {poi['location']}")
    
    print("\n" + "=" * 50)
    print("🎯 优化方案的优势:")
    print("   ✓ 复用现有的 search_along_route() 方法")
    print("   ✓ 每段都会进行完整的路线规划和POI搜索")
    print("   ✓ 保持了原有方法的所有功能特性")
    print("   ✓ 代码逻辑更简洁，维护性更好")
    print("   ✓ 避免了重复实现多边形创建和搜索逻辑")
    
    print("\n💡 实际应用中的调用方式:")
    print("   segment_result = self.search_along_route(")
    print("       origin=segment_origin,")
    print("       destination=segment_destination,")
    print("       keywords=keywords,")
    print("       poi_type_name=poi_type_name,")
    print("       buffer_distance=buffer_distance,")
    print("       strategy=strategy,")
    print("       waypoints='',  # 段内不使用途经点")
    print("       max_vertices=max_points_per_segment * 2")
    print("   )")


if __name__ == "__main__":
    main()
